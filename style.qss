/* === General Window & Font === */
QWidget {
    font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
    background-color: #1e1e2e; /* --bg */
    color: #e0e0e0; /* --text */
}

/* === Floating Button Container === */
#FloatingButtonContainer {
    background-color: transparent;
}

/* === Floating Button === */
#FloatingButton {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #4a6bdf, stop: 1 #3a5bcf);
    border-radius: 25px;
    border: 2px solid #5a7bef;
}

#FloatingButton:hover {
    background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                                stop: 0 #5a7bef, stop: 1 #4a6bdf);
    border: 2px solid #6a8bff;
}

#FloatingButtonIcon {
    font-size: 24px;
    color: white;
    font-weight: bold;
}

/* === Expanded Content === */
#ExpandedContent {
    background-color: #2a2a3a; /* --surface */
    border-radius: 12px;
    border: 1px solid #3a3a4a; /* --surface-2 */
}

#ExpandedTitle {
    font-size: 16px;
    font-weight: 600;
    color: #e0e0e0; /* --text */
}

#CompactSectionTitle {
    font-size: 14px;
    font-weight: 600;
    color: #e0e0e0; /* --text */
    margin-top: 8px;
    margin-bottom: 4px;
}

/* === Compact Translations List === */
#CompactTranslationsList {
    background: transparent;
    border: 1px solid #3a3a4a; /* --surface-2 */
    border-radius: 6px;
}

#CompactTranslationsList::item {
    background: #1e1e2e; /* --bg */
    border-radius: 4px;
    margin: 2px;
    padding: 4px;
}

#CompactTranslationsList::item:selected {
    background: #3a3a4a; /* --surface-2 */
}

/* === Main Window Specific === */
#MainWindowTitle {
    font-size: 24px;
    font-weight: 600;
}

#MainWindowSubtitle {
    font-size: 14px;
    color: #a0a0a0; /* --text-secondary */
}

#SectionTitle {
    font-size: 18px;
    font-weight: 600;
}

/* === Status Pill === */
#StatusPill {
    background: #2a2a3a; /* --surface */
    border-radius: 12px; /* more rounded */
    padding: 6px 12px;
}

#StatusPill QLabel {
    font-size: 14px;
}

#StatusDot {
    width: 8px;
    height: 8px;
    border-radius: 4px;
}

#StatusDot[color="#4CAF50"] {
    background: #4CAF50; /* --success */
}

#StatusDot[color="gray"] {
    background: gray;
}

/* === Buttons === */
QPushButton {
    border-radius: 8px; /* --radius */
    font-weight: 500;
    font-size: 14px;
    padding: 8px 16px;
}

QPushButton[style="primary"] {
    background: #4a6bdf; /* --accent */
    color: white;
    border: none;
}

QPushButton[style="primary"]:hover {
    background: #5a7bef; /* --accent-hover */
}

QPushButton[style="ghost"] {
    background: transparent;
    color: #4a6bdf; /* --accent */
    border: 1px solid #3a3a4a; /* --surface-2 */
}

QPushButton[style="ghost"]:hover {
    background: #2a2a3a; /* --surface */
}

QPushButton[style="text"] {
    background: transparent;
    color: #a0a0a0; /* --text-secondary */
    padding: 4px 8px;
    border: none;
}

QPushButton[style="text"]:hover {
    color: #e0e0e0; /* --text */
}

/* === Translations List === */
#TranslationsList {
    background: transparent;
    border: none;
}

#TranslationsList::item {
    background: #2a2a3a; /* --surface */
    border-radius: 8px; /* --radius */
    margin-bottom: 8px;
}

#TranslationsList::item:selected {
    background: #3a3a4a; /* --surface-2 */
}

/* === List Item Content === */
#TranslationListItemWidget {
    /* This is the container widget inside the list item */
}

#OriginalLabel {
    font-weight: 600;
    color: #e0e0e0; /* --text */
}

#TranslatedLabel {
    color: #a0a0a0; /* --text-secondary */
}

#TimeLabel {
    font-size: 12px;
    color: #a0a0a0; /* --text-secondary */
}

/* === CheckBox === */
QCheckBox {
    color: #a0a0a0; /* --text-secondary */
    font-size: 14px;
    spacing: 5px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border-radius: 4px;
    border: 1px solid #3a3a4a; /* --surface-2 */
    background: #2a2a3a; /* --surface */
}

QCheckBox::indicator:checked {
    background: #4a6bdf; /* --accent */
}

/* === QDialog (General Dialog Styling) === */
QDialog {
    background-color: #1e1e2e; /* Same as main window background */
    border: 1px solid #3a3a4a; /* --surface-2 */
    border-radius: 8px;
}

QDialog QLabel {
    color: #e0e0e0; /* --text */
}

/* === QStackedWidget === */
QStackedWidget {
    background-color: transparent;
}

/* === QRadioButton === */
QRadioButton {
    color: #e0e0e0; /* --text */
    font-size: 14px;
    spacing: 5px;
}

QRadioButton::indicator {
    width: 16px;
    height: 16px;
    border-radius: 8px; /* Make it circular */
    border: 1px solid #3a3a4a; /* --surface-2 */
    background: #2a2a3a; /* --surface */
}

QRadioButton::indicator:checked {
    background: #4a6bdf; /* --accent */
    border: 1px solid #4a6bdf; /* --accent */
}

/* === QLineEdit & QTextEdit === */
QLineEdit, QTextEdit {
    border: 1px solid #3a3a4a; /* --surface-2 */
    border-radius: 6px;
    padding: 8px;
    background-color: #2a2a3a; /* --surface */
    color: #e0e0e0; /* --text */
    selection-background-color: #4a6bdf; /* --accent */
}

QLineEdit:focus, QTextEdit:focus {
    border-color: #4a6bdf; /* --accent */
}

/* === QComboBox === */
QComboBox {
    border: 1px solid #3a3a4a; /* --surface-2 */
    border-radius: 6px;
    padding: 5px;
    background-color: #2a2a3a; /* --surface */
    color: #e0e0e0; /* --text */
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 20px;
    border-left-width: 1px;
    border-left-color: #3a3a4a;
    border-left-style: solid;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}

QComboBox::down-arrow {
    image: url(./icons/down_arrow.png); /* Placeholder for an actual arrow icon */
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    border: 1px solid #3a3a4a;
    border-radius: 6px;
    background-color: #2a2a3a;
    selection-background-color: #4a6bdf;
    color: #e0e0e0;
}

/* === QSpinBox === */
QSpinBox {
    border: 1px solid #3a3a4a; /* --surface-2 */
    border-radius: 6px;
    padding: 5px;
    background-color: #2a2a3a; /* --surface */
    color: #e0e0e0; /* --text */
}

QSpinBox::up-button, QSpinBox::down-button {
    width: 20px;
    border: 1px solid #3a3a4a;
    border-radius: 3px;
    background-color: #2a2a3a;
}

QSpinBox::up-arrow, QSpinBox::down-arrow {
    image: url(./icons/up_arrow.png); /* Placeholder */
    width: 10px;
    height: 10px;
}

/* === CustomPromptWindow === */
#CustomPromptWindowContainer {
    background-color: #2a2a3a; /* --surface */
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #4a6bdf; /* --accent border for floating design */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

#PromptTextLabel {
    font-size: 14px;
    font-weight: 500;
    color: #e0e0e0; /* --text */
    margin-bottom: 8px;
}

#PromptTextLabel i {
    color: #a0a0a0; /* --text-secondary */
    font-size: 12px;
}

/* === CustomResultWindow === */
#CustomResultWindowContainer {
    background-color: #2a2a3a; /* --surface */
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #4a6bdf; /* --accent border for floating design */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

#ResultTitleLabel {
    font-size: 16px;
    font-weight: 600;
    color: #e0e0e0; /* --text */
    margin-bottom: 8px;
}

#ResultOriginalLabel, #ResultTranslationLabel {
    font-size: 14px;
    font-weight: 500;
    color: #e0e0e0; /* --text */
    margin-top: 10px;
    margin-bottom: 5px;
}

#ResultTextEdit {
    background-color: #1e1e2e; /* Slightly darker background for read-only */
    border: 1px solid #3a3a4a;
    border-radius: 6px;
    padding: 8px;
    color: #e0e0e0;
}

#ResultTextEdit:read-only {
    background-color: #1e1e2e; /* Ensure it stays dark */
}
