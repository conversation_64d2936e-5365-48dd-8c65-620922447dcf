
from PySide6.QtWidgets import (
    QWidget, QLabel, QTextEdit, QVBoxLayout, QHBoxLayout, QApplication,
    QScrollArea, QFrame, QSizePolicy
)
from PySide6.QtCore import Qt, QTimer, QPropertyAnimation, QEasingCurve
from ui.widgets import StyledButton
from services.translation.translation_models import TranslationResponse, AlternativeTranslation, CommonExpression, GrammarNote, WordBreakdown, Pronunciation

class CustomResultWindow(QWidget):
    def __init__(self, original: str, translation: str, from_cache: bool, structured_response_json_str: str, parent=None):
        super().__init__(parent)
        self.original_text = original
        self.primary_translation = translation
        self.from_cache = from_cache
        self.parent_window = parent
        
        # The structured_response_json_str is now guaranteed to be a valid JSON string
        # representing a TranslationResponse object, or None.
        # We can directly load it into a TranslationResponse object.
        if structured_response_json_str:
            self.structured_response = TranslationResponse.from_json(structured_response_json_str)
        else:
            # Fallback for cases where structured data might not be available (e.g., old cache entries)
            self.structured_response = TranslationResponse(
                original_text=original,
                primary_translation=translation,
                detected_language="unknown",
                confidence_score=0.0
            )

        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_DeleteOnClose)
        # Set compact size for floating button design
        self.setFixedSize(350, 400)

        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)

        container = QWidget()
        container.setObjectName("CustomResultWindowContainer")
        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(20, 20, 20, 20)
        container_layout.setSpacing(15)

        title_text = "Translation Result"
        if self.from_cache:
            title_text += " (from cache)"
        
        title_label = QLabel(title_text)
        title_label.setObjectName("ResultTitleLabel")
        container_layout.addWidget(title_label)

        # Scroll Area for content
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_content_widget = QWidget()
        self.scroll_content_layout = QVBoxLayout(scroll_content_widget)
        self.scroll_content_layout.setContentsMargins(0, 0, 0, 0)
        self.scroll_content_layout.setSpacing(10)
        scroll_area.setWidget(scroll_content_widget)
        container_layout.addWidget(scroll_area)

        self._add_translation_sections()

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        copy_btn = StyledButton("Copy Primary", "ghost")
        copy_btn.clicked.connect(self.copy_primary_translation)
        button_layout.addWidget(copy_btn)

        close_btn = StyledButton("Close", "primary")
        close_btn.clicked.connect(self.close)
        button_layout.addWidget(close_btn)

        container_layout.addLayout(button_layout)
        main_layout.addWidget(container)

        # Setup auto-dismiss timer (30 seconds for result window)
        self.auto_dismiss_timer = QTimer()
        self.auto_dismiss_timer.timeout.connect(self.close)
        self.auto_dismiss_timer.setSingleShot(True)
        self.auto_dismiss_timer.start(30000)  # 30 seconds

        # Setup fade-in animation
        self.setWindowOpacity(0.0)
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(300)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
        self.fade_animation.start()

    def _add_translation_sections(self):
        # Original Text
        self._add_section_header("Original Text:")
        original_text_edit = QTextEdit(self.original_text)
        original_text_edit.setReadOnly(True)
        original_text_edit.setFixedHeight(60) # Smaller height for original
        original_text_edit.setObjectName("ResultTextEdit")
        self.scroll_content_layout.addWidget(original_text_edit)

        # Primary Translation
        self._add_section_header("Primary Translation:")
        primary_translation_text_edit = QTextEdit(self.primary_translation)
        primary_translation_text_edit.setReadOnly(True)
        primary_translation_text_edit.setFixedHeight(60) # Smaller height for primary
        primary_translation_text_edit.setObjectName("ResultTextEdit")
        self.scroll_content_layout.addWidget(primary_translation_text_edit)

        if self.structured_response:
            # Alternative Translations
            if self.structured_response.alternative_translations:
                self._add_section_header("Alternative Translations:")
                for alt in self.structured_response.alternative_translations:
                    self._add_alternative_translation_widget(alt)

            # Common Expressions
            if self.structured_response.common_expressions:
                self._add_section_header("Common Expressions:")
                for expr in self.structured_response.common_expressions:
                    self._add_common_expression_widget(expr)

            # Grammar Notes
            if self.structured_response.grammar_notes:
                self._add_section_header("Grammar Notes:")
                for note in self.structured_response.grammar_notes:
                    self._add_grammar_note_widget(note)
            
            # Word Breakdown
            if self.structured_response.word_breakdown:
                self._add_section_header("Word Breakdown:")
                for word in self.structured_response.word_breakdown:
                    self._add_word_breakdown_widget(word)

            # Cultural Context
            if self.structured_response.cultural_context:
                self._add_section_header("Cultural Context:")
                cultural_context_label = QLabel(self.structured_response.cultural_context)
                cultural_context_label.setWordWrap(True)
                cultural_context_label.setObjectName("ResultDetailLabel")
                self.scroll_content_layout.addWidget(cultural_context_label)

            # Pronunciation
            if self.structured_response.pronunciation:
                self._add_section_header("Pronunciation:")
                pron_layout = QVBoxLayout()
                pron = self.structured_response.pronunciation
                if pron.pinyin:
                    pron_layout.addWidget(QLabel(f"Pinyin: {pron.pinyin}"))
                if pron.ipa:
                    pron_layout.addWidget(QLabel(f"IPA: {pron.ipa}"))
                if pron.hiragana:
                    pron_layout.addWidget(QLabel(f"Hiragana: {pron.hiragana}"))
                if pron.katakana:
                    pron_layout.addWidget(QLabel(f"Katakana: {pron.katakana}"))
                # Add audio button if audio_url exists
                if pron.audio_url:
                    audio_btn = StyledButton("Play Audio", "ghost")
                    # TODO: Connect to actual audio playback
                    pron_layout.addWidget(audio_btn)
                
                pron_widget = QWidget()
                pron_widget.setLayout(pron_layout)
                pron_widget.setObjectName("ResultDetailFrame")
                self.scroll_content_layout.addWidget(pron_widget)

        self.scroll_content_layout.addStretch() # Push content to top

    def _add_section_header(self, title: str):
        header_label = QLabel(title)
        header_label.setObjectName("SectionSubTitle")
        self.scroll_content_layout.addWidget(header_label)

    def _add_alternative_translation_widget(self, alt: AlternativeTranslation):
        frame = QFrame()
        frame.setObjectName("ResultDetailFrame")
        layout = QVBoxLayout(frame)
        layout.addWidget(QLabel(f"<b>Translation:</b> {alt.translation}"))
        layout.addWidget(QLabel(f"<b>Context:</b> {alt.context}"))
        if alt.usage_notes:
            layout.addWidget(QLabel(f"<b>Notes:</b> {alt.usage_notes}"))
        self.scroll_content_layout.addWidget(frame)

    def _add_common_expression_widget(self, expr: CommonExpression):
        frame = QFrame()
        frame.setObjectName("ResultDetailFrame")
        layout = QVBoxLayout(frame)
        layout.addWidget(QLabel(f"<b>Expression:</b> {expr.expression}"))
        layout.addWidget(QLabel(f"<b>Meaning:</b> {expr.meaning}"))
        layout.addWidget(QLabel(f"<b>Context:</b> {expr.context}"))
        if expr.examples:
            layout.addWidget(QLabel("<b>Examples:</b>"))
            for ex in expr.examples:
                layout.addWidget(QLabel(f"- {ex}"))
        layout.addWidget(QLabel(f"<b>Difficulty:</b> {expr.difficulty_level.capitalize()}"))
        if expr.reading:
            # Assuming expr.reading is a pinyin string for common expressions
            pron_obj = Pronunciation(pinyin=expr.reading)
            self._add_pronunciation_widget(layout, pron_obj)
        self.scroll_content_layout.addWidget(frame)

    def _add_grammar_note_widget(self, note: GrammarNote):
        frame = QFrame()
        frame.setObjectName("ResultDetailFrame")
        layout = QVBoxLayout(frame)
        layout.addWidget(QLabel(f"<b>Pattern:</b> {note.pattern}"))
        layout.addWidget(QLabel(f"<b>Explanation:</b> {note.explanation}"))
        if note.examples:
            layout.addWidget(QLabel("<b>Examples:</b>"))
            for ex in note.examples:
                layout.addWidget(QLabel(f"- {ex}"))
        self.scroll_content_layout.addWidget(frame)

    def _add_word_breakdown_widget(self, word: WordBreakdown):
        frame = QFrame()
        frame.setObjectName("ResultDetailFrame")
        layout = QVBoxLayout(frame)
        layout.addWidget(QLabel(f"<b>Character:</b> {word.character}"))
        if word.meaning:
            layout.addWidget(QLabel(f"<b>Meaning:</b> {word.meaning}"))
        if word.reading:
            layout.addWidget(QLabel(f"<b>Reading:</b> {word.reading}"))
        if word.part_of_speech:
            layout.addWidget(QLabel(f"<b>Part of Speech:</b> {word.part_of_speech}"))
        if word.radical:
            layout.addWidget(QLabel(f"<b>Radical:</b> {word.radical}"))
        if word.stroke_count:
            layout.addWidget(QLabel(f"<b>Stroke Count:</b> {word.stroke_count}"))
        self.scroll_content_layout.addWidget(frame)

    def _add_pronunciation_widget(self, parent_layout: QVBoxLayout, pron: Pronunciation):
        pron_layout = QVBoxLayout()
        if pron.pinyin:
            pron_layout.addWidget(QLabel(f"Pinyin: {pron.pinyin}"))
        if pron.ipa:
            pron_layout.addWidget(QLabel(f"IPA: {pron.ipa}"))
        if pron.hiragana:
            pron_layout.addWidget(QLabel(f"Hiragana: {pron.hiragana}"))
        if pron.katakana:
            pron_layout.addWidget(QLabel(f"Katakana: {pron.katakana}"))
        if pron.audio_url:
            audio_btn = StyledButton("Play Audio", "ghost")
            # TODO: Connect to actual audio playback
            pron_layout.addWidget(audio_btn)
        
        if pron_layout.count() > 0:
            pron_widget = QWidget()
            pron_widget.setLayout(pron_layout)
            pron_widget.setObjectName("ResultDetailSubFrame") # Use a different object name for sub-frames
            parent_layout.addWidget(pron_widget)

    def copy_primary_translation(self):
        clipboard = QApplication.clipboard()
        clipboard.setText(self.primary_translation)

    def close(self):
        """Override close to stop auto-dismiss timer"""
        if hasattr(self, 'auto_dismiss_timer'):
            self.auto_dismiss_timer.stop()
        super().close()
