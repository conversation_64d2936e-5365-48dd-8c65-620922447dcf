

# ui/custom_prompt_window.py
from PySide6.QtWidgets import QWidget, QLabel, QVBoxLayout, QHBoxLayout
from PySide6.QtCore import Qt, Signal, QTimer, QPropertyAnimation, QEasingCurve
from ui.widgets import StyledButton # Import our custom button

class CustomPromptWindow(QWidget):
    user_responded = Signal(bool)

    def __init__(self, text, cache_info, parent=None):
        super().__init__(parent)
        self.text_to_translate = text
        self.parent_window = parent

        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setAttribute(Qt.WA_DeleteOnClose)

        # Set fixed size for compact design
        self.setFixedSize(280, 120)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0) # Remove outer margins

        container = QWidget()
        container.setObjectName("CustomPromptWindowContainer") # Set object name for styling
        container_layout = QVBoxLayout(container)
        container_layout.setContentsMargins(20, 20, 20, 20) # Inner padding
        container_layout.setSpacing(15)

        # Truncate text for compact display
        display_text = text[:50] + "..." if len(text) > 50 else text
        prompt_label = QLabel(f"Translate this text?{cache_info}\n<i>{display_text}</i>")
        prompt_label.setObjectName("PromptTextLabel") # Set object name for styling
        prompt_label.setWordWrap(True) # Ensure text wraps
        container_layout.addWidget(prompt_label)

        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        button_layout.addStretch() # Push buttons to the right

        yes_btn = StyledButton("Yes", "primary") # Use StyledButton
        no_btn = StyledButton("No", "ghost") # Use StyledButton

        yes_btn.clicked.connect(self.accept_translation)
        no_btn.clicked.connect(self.reject_translation)

        button_layout.addWidget(no_btn)
        button_layout.addWidget(yes_btn)
        container_layout.addLayout(button_layout)

        layout.addWidget(container)

        # Setup auto-dismiss timer (15 seconds)
        self.auto_dismiss_timer = QTimer()
        self.auto_dismiss_timer.timeout.connect(self.auto_dismiss)
        self.auto_dismiss_timer.setSingleShot(True)
        self.auto_dismiss_timer.start(15000)  # 15 seconds

        # Setup fade-in animation
        self.setWindowOpacity(0.0)
        self.fade_animation = QPropertyAnimation(self, b"windowOpacity")
        self.fade_animation.setDuration(200)
        self.fade_animation.setStartValue(0.0)
        self.fade_animation.setEndValue(1.0)
        self.fade_animation.setEasingCurve(QEasingCurve.OutCubic)
        self.fade_animation.start()

    def accept_translation(self):
        self.auto_dismiss_timer.stop()
        self.user_responded.emit(True)
        self.close()

    def reject_translation(self):
        self.auto_dismiss_timer.stop()
        self.user_responded.emit(False)
        self.close()

    def auto_dismiss(self):
        """Auto-dismiss after timeout - defaults to reject"""
        self.user_responded.emit(False)
        self.close()

