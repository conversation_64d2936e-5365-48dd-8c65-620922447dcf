import sys
import os
from PySide6.QtWidgets import (
    QApp<PERSON>, QMainWindow, QStyle, QWidget, QLabel, QCheckBox,
    QVBoxLayout, QHBoxLayout, QListWidget,
    QMessageBox, QSystemTrayIcon, QMenu, QFrame
)
from PySide6.QtCore import Qt, QTimer, Slot, QPropertyAnimation, QEasingCurve, QRect, QPoint, QSize
from PySide6.QtGui import QAction, QScreen

# Local imports
from manager.config_manager import ConfigManager
from manager.cache_manager import CacheManager
from services.clipboard.clipboard_service import ClipboardService
from ui.settings_window import SettingsWindow
from ui.widgets import StatusPill, TranslationListItem, StyledButton

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Clipboard Translator")

        # Floating button properties
        self.collapsed_size = QSize(50, 50)
        self.expanded_size = QSize(300, 400)
        self.is_expanded = False
        self.screen_edge_margin = 10

        # Managers
        self.config_manager = ConfigManager(os.path.expanduser("~/.clipboard_translator_config.json"))
        self.cache_manager = CacheManager(os.path.expanduser("~/.clipboard_translator_cache.db"))
        self.clipboard_service = None
        self.prompt_window = None
        self.result_window = None

        # Setup floating window properties
        self.setup_floating_window()

        # Setup system tray
        self.setup_system_tray()

        # Create main UI
        self.setup_ui()

        # Position at screen edge
        self.position_at_screen_edge()

        # Auto-start if configured
        if self.config_manager.get('auto_start', False):
            QTimer.singleShot(100, self.start_service)

    def setup_floating_window(self):
        """Configure window for floating behavior"""
        # Remove window decorations and make it stay on top
        self.setWindowFlags(
            Qt.FramelessWindowHint |
            Qt.WindowStaysOnTopHint |
            Qt.Tool
        )

        # Set initial size to collapsed
        self.setFixedSize(self.collapsed_size)

        # Make window semi-transparent when collapsed
        self.setWindowOpacity(0.8)

        # Setup animations
        self.setup_animations()

    def setup_animations(self):
        """Setup smooth animations for expand/collapse"""
        self.resize_animation = QPropertyAnimation(self, b"geometry")
        self.resize_animation.setDuration(200)
        self.resize_animation.setEasingCurve(QEasingCurve.OutCubic)

        self.opacity_animation = QPropertyAnimation(self, b"windowOpacity")
        self.opacity_animation.setDuration(150)
        self.opacity_animation.setEasingCurve(QEasingCurve.OutCubic)

    def position_at_screen_edge(self):
        """Position the floating button at the right edge of the screen"""
        # Use primary screen for initial positioning
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()

        # Position at right edge, vertically centered
        x = screen_geometry.x() + screen_geometry.width() - self.collapsed_size.width() - self.screen_edge_margin
        y = screen_geometry.y() + (screen_geometry.height() - self.collapsed_size.height()) // 2

        self.move(x, y)

        # Store the current screen for later reference
        self.current_screen = screen

    def setup_ui(self):
        """Setup the floating button UI"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        central_widget.setObjectName("FloatingButtonContainer")

        # Main layout
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # Floating button (collapsed state)
        self.floating_button = QFrame()
        self.floating_button.setObjectName("FloatingButton")
        self.floating_button.setFixedSize(self.collapsed_size)
        self.floating_button.mousePressEvent = self.toggle_expansion

        button_layout = QVBoxLayout(self.floating_button)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setAlignment(Qt.AlignCenter)

        # Icon/indicator for the floating button
        self.button_icon = QLabel("📋")
        self.button_icon.setObjectName("FloatingButtonIcon")
        self.button_icon.setAlignment(Qt.AlignCenter)
        button_layout.addWidget(self.button_icon)

        # Add hover effects
        self.floating_button.enterEvent = self.on_button_hover_enter
        self.floating_button.leaveEvent = self.on_button_hover_leave

        main_layout.addWidget(self.floating_button)

        # Expanded content (initially hidden)
        self.expanded_content = QFrame()
        self.expanded_content.setObjectName("ExpandedContent")
        self.expanded_content.hide()

        self.setup_expanded_content()
        main_layout.addWidget(self.expanded_content)

        # Initial refresh of translations and status
        self.refresh_recent_translations()
        self.update_service_status_ui()

    def setup_expanded_content(self):
        """Setup the expanded dropdown content"""
        content_layout = QVBoxLayout(self.expanded_content)
        content_layout.setContentsMargins(10, 10, 10, 10)
        content_layout.setSpacing(8)

        # Header with title and close button
        header = QFrame()
        header_layout = QHBoxLayout(header)
        header_layout.setContentsMargins(0, 0, 0, 0)

        title = QLabel("Clipboard Translator")
        title.setObjectName("ExpandedTitle")

        close_btn = StyledButton("×", "text")
        close_btn.setFixedSize(20, 20)
        close_btn.clicked.connect(self.collapse)

        header_layout.addWidget(title)
        header_layout.addStretch()
        header_layout.addWidget(close_btn)
        content_layout.addWidget(header)

        # Status and service controls
        self.status_pill = StatusPill("Service Status: Checking...")
        content_layout.addWidget(self.status_pill)

        service_controls = QFrame()
        service_layout = QHBoxLayout(service_controls)
        service_layout.setContentsMargins(0, 0, 0, 0)
        service_layout.setSpacing(5)

        self.start_btn = StyledButton("Start", "primary")
        self.start_btn.clicked.connect(self.start_service)

        self.stop_btn = StyledButton("Stop", "ghost")
        self.stop_btn.clicked.connect(self.stop_service)
        self.stop_btn.setEnabled(False)

        service_layout.addWidget(self.start_btn)
        service_layout.addWidget(self.stop_btn)
        service_layout.addStretch()
        content_layout.addWidget(service_controls)

        # Recent translations (compact list)
        translations_label = QLabel("Recent Translations")
        translations_label.setObjectName("CompactSectionTitle")
        content_layout.addWidget(translations_label)

        self.translations_list = QListWidget()
        self.translations_list.setObjectName("CompactTranslationsList")
        self.translations_list.setMaximumHeight(150)
        content_layout.addWidget(self.translations_list)

        # Action buttons
        actions = QFrame()
        actions_layout = QHBoxLayout(actions)
        actions_layout.setContentsMargins(0, 0, 0, 0)
        actions_layout.setSpacing(5)

        settings_btn = StyledButton("Settings", "text")
        settings_btn.clicked.connect(self.open_settings_dialog)

        cache_btn = StyledButton("Cache", "text")
        cache_btn.clicked.connect(self.open_cache_manager_dialog)

        refresh_btn = StyledButton("Refresh", "text")
        refresh_btn.clicked.connect(self.refresh_recent_translations)

        actions_layout.addWidget(settings_btn)
        actions_layout.addWidget(cache_btn)
        actions_layout.addWidget(refresh_btn)
        actions_layout.addStretch()
        content_layout.addWidget(actions)

        # Auto-start checkbox
        self.auto_start_check = QCheckBox("Auto-start service")
        self.auto_start_check.setChecked(self.config_manager.get('auto_start', False))
        self.auto_start_check.toggled.connect(self.toggle_auto_start)
        content_layout.addWidget(self.auto_start_check)

    def toggle_expansion(self, event):
        """Toggle between collapsed and expanded states"""
        if self.is_expanded:
            self.collapse()
        else:
            self.expand()

    def expand(self):
        """Expand the floating button to show full interface"""
        if self.is_expanded:
            return

        self.is_expanded = True

        # Calculate new position (expand leftward from right edge)
        current_pos = self.pos()
        new_width = self.expanded_size.width()
        new_x = current_pos.x() - (new_width - self.collapsed_size.width())

        # Ensure expanded window stays within screen bounds
        if hasattr(self, 'current_screen') and self.current_screen:
            screen_geometry = self.current_screen.availableGeometry()
            if new_x < screen_geometry.x():
                new_x = screen_geometry.x() + 10  # Small margin from left edge

        # Animate resize and position
        start_geometry = QRect(current_pos.x(), current_pos.y(),
                              self.collapsed_size.width(), self.collapsed_size.height())
        end_geometry = QRect(new_x, current_pos.y(),
                            new_width, self.expanded_size.height())

        self.resize_animation.setStartValue(start_geometry)
        self.resize_animation.setEndValue(end_geometry)

        # Show expanded content and hide floating button
        self.floating_button.hide()
        self.expanded_content.show()

        # Make fully opaque when expanded
        self.opacity_animation.setStartValue(0.8)
        self.opacity_animation.setEndValue(1.0)

        self.resize_animation.start()
        self.opacity_animation.start()

    def collapse(self):
        """Collapse the interface back to floating button"""
        if not self.is_expanded:
            return

        self.is_expanded = False

        # Calculate position back to right edge
        current_geometry = self.geometry()
        new_x = current_geometry.x() + (current_geometry.width() - self.collapsed_size.width())

        start_geometry = current_geometry
        end_geometry = QRect(new_x, current_geometry.y(),
                            self.collapsed_size.width(), self.collapsed_size.height())

        self.resize_animation.setStartValue(start_geometry)
        self.resize_animation.setEndValue(end_geometry)

        # Hide expanded content and show floating button
        self.expanded_content.hide()
        self.floating_button.show()

        # Make semi-transparent when collapsed
        self.opacity_animation.setStartValue(1.0)
        self.opacity_animation.setEndValue(0.8)

        self.resize_animation.start()
        self.opacity_animation.start()

    def position_popup_window(self, popup_window):
        """Position popup windows near the floating button"""
        # Get the floating button position
        button_geometry = self.geometry()

        # Position popup to the left of the floating button
        popup_x = button_geometry.x() - popup_window.width() - 10
        popup_y = button_geometry.y()

        # Ensure popup stays within screen bounds
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()

        if popup_x < screen_geometry.x():
            popup_x = button_geometry.x() + button_geometry.width() + 10

        if popup_y + popup_window.height() > screen_geometry.height():
            popup_y = screen_geometry.height() - popup_window.height() - 10

        popup_window.move(popup_x, popup_y)

    def on_button_hover_enter(self, event):
        """Handle mouse hover enter on floating button"""
        if not self.is_expanded:
            self.setWindowOpacity(1.0)

    def on_button_hover_leave(self, event):
        """Handle mouse hover leave on floating button"""
        if not self.is_expanded:
            self.setWindowOpacity(0.8)

    def setup_system_tray(self):
        if not QSystemTrayIcon.isSystemTrayAvailable():
            return

        self.tray_icon = QSystemTrayIcon(self)
        self.tray_icon.setIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))

        tray_menu = QMenu()
        self.start_action_tray = QAction("Start Service", self)
        self.start_action_tray.triggered.connect(self.start_service)
        tray_menu.addAction(self.start_action_tray)

        self.stop_action_tray = QAction("Stop Service", self)
        self.stop_action_tray.triggered.connect(self.stop_service)
        self.stop_action_tray.setEnabled(False)
        tray_menu.addAction(self.stop_action_tray)

        tray_menu.addSeparator()
        settings_action = QAction("Settings", self)
        settings_action.triggered.connect(self.open_settings_dialog)
        tray_menu.addAction(settings_action)

        cache_action = QAction("Manage Cache", self)
        cache_action.triggered.connect(self.open_cache_manager_dialog)
        tray_menu.addAction(cache_action)

        tray_menu.addSeparator()
        quit_action = QAction("Quit", self)
        quit_action.triggered.connect(QApplication.instance().quit)
        tray_menu.addAction(quit_action)

        self.tray_icon.setContextMenu(tray_menu)
        self.tray_icon.show()
        self.tray_icon.activated.connect(self.on_tray_icon_activated)

    def on_tray_icon_activated(self, reason):
        if reason == QSystemTrayIcon.Trigger or reason == QSystemTrayIcon.DoubleClick:
            self.show()
            self.raise_()
            self.activateWindow()

    def get_service_display_name(self):
        config = self.config_manager.config
        service = config.get('translation_service', 'unknown')
        if service == 'ollama':
            return f"Ollama ({config.get('ollama_model', 'N/A')})"
        elif service == 'gemini':
             return f"Gemini ({config.get('gemini_model', 'N/A')})"
        elif service == 'custom':
            model_name = config.get('custom_model', 'N/A')
            return f"Custom API ({model_name if model_name else 'N/A'})"
        return f"{str(service).capitalize()} (Unknown)"

    def update_service_status_ui(self):
        service_name = self.get_service_display_name()
        is_running = self.clipboard_service is not None and self.clipboard_service.isRunning()

        if is_running:
            self.status_pill.label.setText(f"{service_name} - Running")
            self.status_pill.dot.setProperty("color", "#4CAF50")
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            if hasattr(self, 'tray_icon'):
                self.start_action_tray.setEnabled(False)
                self.stop_action_tray.setEnabled(True)
        else:
            self.status_pill.label.setText(f"{service_name} - Stopped")
            self.status_pill.dot.setProperty("color", "gray")
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            if hasattr(self, 'tray_icon'):
                self.start_action_tray.setEnabled(True)
                self.stop_action_tray.setEnabled(False)
        
        # Re-apply style to update dot color
        self.status_pill.dot.style().unpolish(self.status_pill.dot)
        self.status_pill.dot.style().polish(self.status_pill.dot)

    def toggle_auto_start(self):
        is_checked = self.auto_start_check.isChecked()
        self.config_manager.set('auto_start', is_checked)
        try:
            self.config_manager.save_config(self.config_manager.config)
        except Exception as e:
             QMessageBox.critical(self, "Settings Error", f"Failed to save auto-start setting: {e}")

    def start_service(self):
        if self.clipboard_service is not None and self.clipboard_service.isRunning():
            return

        if self.clipboard_service is not None:
            self.clipboard_service = None

        try:
            self.clipboard_service = ClipboardService(self.config_manager, self.cache_manager)
            self.clipboard_service.translation_requested.connect(self.show_pyside6_translation_prompt)
            self.clipboard_service.translation_done.connect(self.show_pyside6_translation_result)
            self.clipboard_service.error_occurred.connect(self.show_pyside6_error_message)
            self.clipboard_service.finished.connect(self.on_clipboard_service_finished)
            self.clipboard_service.start()
            self.update_service_status_ui()
        except Exception as e:
            QMessageBox.critical(self, "Service Error", f"Failed to start clipboard service: {e}")
            if self.clipboard_service:
                self.clipboard_service = None
            self.update_service_status_ui()

    def stop_service(self):
        if self.clipboard_service is None or not self.clipboard_service.isRunning():
            return

        self.clipboard_service.stop()
        self.clipboard_service.quit()
        if not self.clipboard_service.wait(5000):
            self.clipboard_service.terminate()
            self.clipboard_service.wait(1000)

    def on_clipboard_service_finished(self):
        if self.clipboard_service:
            self.clipboard_service.deleteLater()
            self.clipboard_service = None
        self.update_service_status_ui()
        if hasattr(self, 'tray_icon'):
            self.tray_icon.showMessage("Clipboard Translator", "Service Stopped", QSystemTrayIcon.Information, 2000)

    def refresh_recent_translations(self):
        self.translations_list.clear()
        try:
            results = self.cache_manager.get_recent_translations(limit=20)
            for original, translation, accessed_at, is_structured in results:
                # For display in the list, we still show simple translation
                # The structured info is used in the detailed result window
                item = TranslationListItem(
                    original[:50] + '...' if len(original) > 50 else original,
                    translation[:50] + '...' if len(translation) > 50 else translation,
                    accessed_at[:19] + (" (S)" if is_structured else "") # Add (S) for structured
                )
                self.translations_list.addItem(item)
                self.translations_list.setItemWidget(item, item.widget)
        except Exception as e:
            QMessageBox.warning(self, "Cache Error", f"Recent translations error: {e}")

    @Slot(str)
    def show_pyside6_translation_prompt(self, text: str):
        from ui.custom_prompt_window import CustomPromptWindow
        cached_translation = self.cache_manager.get_cached_translation(text)
        cache_info = " (Cached)" if cached_translation else ""
        self.prompt_window = CustomPromptWindow(text, cache_info, self)
        self.prompt_window.user_responded.connect(self.on_prompt_response)

        # Position prompt window near the floating button
        self.position_popup_window(self.prompt_window)
        self.prompt_window.show()

    def on_prompt_response(self, translate: bool):
        if translate:
            text_to_translate = self.prompt_window.text_to_translate
            if self.clipboard_service and self.clipboard_service.isRunning():
                self.clipboard_service.translate_text(text_to_translate)
        self.prompt_window = None

    @Slot(str, str, bool, str)
    def show_pyside6_translation_result(self, original: str, translation: str, from_cache: bool, structured_response_json_str: str):
        from ui.custom_result_window import CustomResultWindow
        self.refresh_recent_translations()
        self.result_window = CustomResultWindow(original, translation, from_cache, structured_response_json_str, self)

        # Position result window near the floating button
        self.position_popup_window(self.result_window)
        self.result_window.show()

        if hasattr(self, 'tray_icon'):
            self.tray_icon.showMessage(
                "Translation Complete",
                f"Original: {original[:30]}...",
                QSystemTrayIcon.Information,
                3000
            )

    @Slot(str)
    def show_pyside6_error_message(self, message: str):
        QMessageBox.critical(self, "Clipboard Translator Error", message)

    def open_settings_dialog(self):
        settings_window = SettingsWindow(self.config_manager, self)
        settings_window.config_updated.connect(self.on_config_updated)
        settings_window.exec()

    def on_config_updated(self, new_config):
        self.update_service_status_ui()
        if self.clipboard_service and self.clipboard_service.isRunning():
            self.stop_service()
            QTimer.singleShot(1000, self.start_service)

    def open_cache_manager_dialog(self):
        QMessageBox.information(self, "Cache Manager", "Cache manager dialog is not yet implemented.")

    def closeEvent(self, event):
        if self.clipboard_service is not None and self.clipboard_service.isRunning():
            self.stop_service()
            if self.clipboard_service and not self.clipboard_service.wait(2000):
                pass
        if hasattr(self, 'tray_icon'):
            self.tray_icon.hide()
        event.accept()
