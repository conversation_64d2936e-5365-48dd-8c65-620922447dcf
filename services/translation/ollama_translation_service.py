from langchain_ollama import ChatOllama
from langchain.schema import HumanMessage
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import RunnablePassthrough, RunnableLambda
import json
import re

from services.translation.translation_service import TranslationServiceHandler
from services.translation.translation_models import TranslationResponse
from services.translation.prompt_templates import get_simple_prompt

# Enhanced JSON parser with fallback for Ollama models
class RobustJsonOutputParser(JsonOutputParser):
    """Enhanced JSON parser with better error handling for Ollama models."""
    
    def parse(self, text: str) -> dict:
        # First, try to extract <PERSON><PERSON><PERSON> from markdown code blocks if present
        json_match = re.search(r'```(?:json)?\s*({.*?})\s*```', text, re.DOTALL)
        if json_match:
            text = json_match.group(1)
        
        # Remove any leading/trailing text that isn't part of JSON
        # Find the first '{' and last '}' to extract JSON object
        start = text.find('{')
        end = text.rfind('}')
        if start != -1 and end != -1 and start < end:
            text = text[start:end+1]
        
        try:
            # Try parsing with the standard parser first
            return super().parse(text)
        except Exception as e:
            # If that fails, try with more aggressive cleaning
            try:
                # Remove any non-JSON text before/after
                cleaned_text = re.sub(r'[^\{\}\[\]:,\"\d\.\-\s\w]', '', text)
                # Ensure balanced brackets (simplified approach)
                open_braces = cleaned_text.count('{')
                close_braces = cleaned_text.count('}')
                while open_braces > close_braces:
                    cleaned_text += '}'
                    close_braces += 1
                
                return json.loads(cleaned_text)
            except Exception as e2:
                # If all else fails, raise the original error with context
                raise ValueError(f"Failed to parse JSON from text: {text[:200]}... Error: {str(e)}") from e2

def create_ollama_json_chain(model_name: str = "gemma3:12b", pydantic_model = TranslationResponse):
    """
    Creates a robust JSON output chain specifically for Ollama models.
    """
    # Initialize the Ollama model
    model = ChatOllama(model=model_name, temperature=0)
    
    # Initialize our enhanced JSON parser
    parser = RobustJsonOutputParser(pydantic_object=pydantic_model)
    
    # Create a highly explicit prompt template for Ollama models
    prompt = PromptTemplate(
        template="""You are a precise JSON output generator. Respond ONLY with valid JSON that matches the provided schema.

CRITICAL INSTRUCTIONS:
1. Output ONLY raw JSON without any markdown formatting (no ```json blocks)
2. Do not include any explanations, notes, or extra text
3. Follow the schema exactly as specified
4. If information is unavailable, use null for optional fields
5. Ensure all required fields are present
6. Use double quotes for all strings
7. Do not include any trailing commas
8. Make sure all objects and arrays are properly closed

Input text to analyze: {input_text}

Schema to follow: {format_instructions}

Your JSON response:""",
        input_variables=["input_text"],
        partial_variables={"format_instructions": parser.get_format_instructions()}
    )
    
    # Create the chain with additional validation step
    chain = (
        {"input_text": RunnablePassthrough()} 
        | prompt 
        | model 
        | RunnableLambda(lambda x: x.content if hasattr(x, 'content') else str(x))  # Extract content
        | parser  # Parse and validate JSON
    )
    
    return chain

class OllamaTranslationService(TranslationServiceHandler):
    def __init__(self, model_name: str):
        self.model_name = model_name
        self.chat_model = ChatOllama(model=self.model_name)
        # The parser is now part of the chain creation, not directly stored here
        self.json_chain = create_ollama_json_chain(model_name=self.model_name, pydantic_model=TranslationResponse)

    def translate(self, text: str) -> str:
        """Backward compatibility method - returns simple translation."""
        try:
            messages = [HumanMessage(content=get_simple_prompt(text))]
            response = self.chat_model.invoke(messages)
            return response.content.strip()
        except Exception as e:
            raise Exception(f"Ollama translation error: {str(e)}") from e

    def translate_structured(self, text: str) -> TranslationResponse:
        """Return structured translation response with comprehensive analysis."""
        try:
            # Use the pre-configured JSON chain
            structured_response = self.json_chain.invoke(text)
            return TranslationResponse.model_validate(structured_response)
                
        except Exception as e:
            # Fallback for parsing errors or other issues: return a basic structured response
            print(f"Ollama structured translation error: {str(e)}. Falling back to basic response.")
            return TranslationResponse(
                original_text=text,
                primary_translation=f"Error: Could not get structured translation. {str(e)}",
                detected_language="unknown",
                confidence_score=0.0
            )
