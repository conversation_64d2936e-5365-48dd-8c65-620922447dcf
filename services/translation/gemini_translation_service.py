from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.schema import HumanMessage
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import <PERSON><PERSON><PERSON><PERSON><PERSON>through

from services.translation.translation_service import <PERSON><PERSON>erviceHandler
from services.translation.translation_models import TranslationResponse
from services.translation.prompt_templates import get_simple_prompt

class GeminiTranslationService(TranslationServiceHandler):
    def __init__(self, api_key: str, model_name: str):
        if not api_key:
             raise ValueError("Gemini API key is required.")
        self.api_key = api_key
        self.model_name = model_name
        self.chat_model = ChatGoogleGenerativeAI(
            model=self.model_name, 
            api_key=self.api_key,
            temperature=0,  # Use 0 for consistent structured output
            max_retries=2,
        )
        self.json_chain = self._create_service_json_chain()

    def _create_service_json_chain(self):
        """Create JSON output chain using the service's model configuration."""
        parser = JsonOutputParser(pydantic_object=TranslationResponse)
        
        prompt = PromptTemplate(
            template="""You are a precise JSON output generator. Respond ONLY with valid JSON that matches the provided schema.
            
Instructions:
1. Analyze the input text
2. Generate a response following the schema exactly
3. Output ONLY the raw JSON without any markdown, explanations, or extra text
4. Ensure all fields are properly filled according to their descriptions 
5. If any information is unavailable, use null for optional fields
6. Provide comprehensive language learning analysis including alternative translations, pronunciations were needed, common expressions, grammar notes, and cultural context

Input text: {input_text}

Format Instructions: {format_instructions}

Your JSON response:""",
            input_variables=["input_text"],
            partial_variables={"format_instructions": parser.get_format_instructions()}
        )
        
        chain = (
            {"input_text": RunnablePassthrough()} 
            | prompt 
            | self.chat_model
            | parser
        )
        
        return chain

    def translate(self, text: str) -> str:
        """Backward compatibility method - returns simple translation."""
        try:
            messages = [HumanMessage(content=get_simple_prompt(text))]
            response = self.chat_model.invoke(messages)
            return response.content.strip()
        except Exception as e:
            raise Exception(f"Gemini translation error: {str(e)}") from e

    def translate_structured(self, text: str) -> TranslationResponse:
        """Return structured translation response with comprehensive analysis using robust JSON chain."""
        try:
            # Use the configured JSON chain for reliable structured output
            result_dict = self.json_chain.invoke(text)
            print(f"Gemini structured translation result: {result_dict}")
            # Explicitly convert the dictionary to TranslationResponse Pydantic object
            return TranslationResponse.model_validate(result_dict)
            
        except Exception as e:
            print(f"Gemini structured translation error: {str(e)}. Falling back to basic response.")
            return TranslationResponse(
                original_text=text,
                primary_translation=f"Error: Could not get structured translation. {str(e)}",
                detected_language="unknown",
                confidence_score=0.0
            )
